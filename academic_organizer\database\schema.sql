-- Academic Organizer Database Schema
-- SQL script to create tables for the academic organizer application

-- Enable foreign key constraints (for SQLite)
PRAGMA foreign_keys = ON;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(80) UNIQUE NOT NULL,
    email VARCHAR(120) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_users_username (username),
    INDEX idx_users_email (email),
    INDEX idx_users_created_at (created_at)
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    user_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate category names per user
    UNIQUE(name, user_id),
    
    -- Indexes
    INDEX idx_categories_user_id (user_id),
    INDEX idx_categories_name (name)
);

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INTEGER,
    tags TEXT, -- JSON string of tags
    category VARCHAR(100),
    user_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes for performance
    INDEX idx_documents_user_id (user_id),
    INDEX idx_documents_title (title),
    INDEX idx_documents_file_type (file_type),
    INDEX idx_documents_category (category),
    INDEX idx_documents_created_at (created_at),
    INDEX idx_documents_updated_at (updated_at)
);

-- Document tags table (normalized approach for better querying)
CREATE TABLE IF NOT EXISTS document_tags (
    id VARCHAR(36) PRIMARY KEY,
    document_id VARCHAR(36) NOT NULL,
    tag_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate tags per document
    UNIQUE(document_id, tag_name),
    
    -- Indexes
    INDEX idx_document_tags_document_id (document_id),
    INDEX idx_document_tags_tag_name (tag_name)
);

-- User sessions table (optional, for session management)
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_user_sessions_user_id (user_id),
    INDEX idx_user_sessions_token (session_token),
    INDEX idx_user_sessions_expires_at (expires_at)
);

-- Document sharing table (for future feature)
CREATE TABLE IF NOT EXISTS document_shares (
    id VARCHAR(36) PRIMARY KEY,
    document_id VARCHAR(36) NOT NULL,
    shared_by_user_id VARCHAR(36) NOT NULL,
    shared_with_user_id VARCHAR(36) NOT NULL,
    permission_level ENUM('read', 'write', 'admin') DEFAULT 'read',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (shared_by_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (shared_with_user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate shares
    UNIQUE(document_id, shared_with_user_id),
    
    -- Indexes
    INDEX idx_document_shares_document_id (document_id),
    INDEX idx_document_shares_shared_by (shared_by_user_id),
    INDEX idx_document_shares_shared_with (shared_with_user_id)
);

-- Document versions table (for version control)
CREATE TABLE IF NOT EXISTS document_versions (
    id VARCHAR(36) PRIMARY KEY,
    document_id VARCHAR(36) NOT NULL,
    version_number INTEGER NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    change_description TEXT,
    created_by_user_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique constraint for version numbers per document
    UNIQUE(document_id, version_number),
    
    -- Indexes
    INDEX idx_document_versions_document_id (document_id),
    INDEX idx_document_versions_version (version_number),
    INDEX idx_document_versions_created_at (created_at)
);

-- Activity log table (for audit trail)
CREATE TABLE IF NOT EXISTS activity_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    action VARCHAR(100) NOT NULL, -- 'create', 'update', 'delete', 'share', etc.
    resource_type VARCHAR(50) NOT NULL, -- 'document', 'category', 'user', etc.
    resource_id VARCHAR(36),
    details TEXT, -- JSON string with additional details
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_activity_logs_user_id (user_id),
    INDEX idx_activity_logs_action (action),
    INDEX idx_activity_logs_resource_type (resource_type),
    INDEX idx_activity_logs_created_at (created_at)
);

-- Create triggers for updating timestamps (SQLite specific)
-- Trigger for users table
CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
    AFTER UPDATE ON users
    FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Trigger for documents table
CREATE TRIGGER IF NOT EXISTS update_documents_timestamp 
    AFTER UPDATE ON documents
    FOR EACH ROW
BEGIN
    UPDATE documents SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Insert default categories (optional)
-- These will be created for each new user via application logic

-- Create views for common queries
-- View for document statistics per user
CREATE VIEW IF NOT EXISTS user_document_stats AS
SELECT 
    u.id as user_id,
    u.username,
    COUNT(d.id) as total_documents,
    SUM(d.file_size) as total_file_size,
    COUNT(DISTINCT d.category) as total_categories,
    MAX(d.created_at) as last_upload_date
FROM users u
LEFT JOIN documents d ON u.id = d.user_id
GROUP BY u.id, u.username;

-- View for recent documents
CREATE VIEW IF NOT EXISTS recent_documents AS
SELECT 
    d.*,
    u.username as owner_username
FROM documents d
JOIN users u ON d.user_id = u.id
ORDER BY d.created_at DESC;

-- View for document tags (flattened)
CREATE VIEW IF NOT EXISTS document_tags_view AS
SELECT 
    d.id as document_id,
    d.title,
    d.user_id,
    dt.tag_name
FROM documents d
JOIN document_tags dt ON d.id = dt.document_id;

-- Comments for documentation
COMMENT ON TABLE users IS 'Stores user account information';
COMMENT ON TABLE categories IS 'User-defined categories for organizing documents';
COMMENT ON TABLE documents IS 'Main table for storing document metadata';
COMMENT ON TABLE document_tags IS 'Normalized tags for documents';
COMMENT ON TABLE user_sessions IS 'Active user sessions for authentication';
COMMENT ON TABLE document_shares IS 'Document sharing permissions between users';
COMMENT ON TABLE document_versions IS 'Version history for documents';
COMMENT ON TABLE activity_logs IS 'Audit trail for user actions';
