"use strict";Object.defineProperty(exports,"__esModule",{value:!0});class t extends Error{reason;filename;line;column;source;constructor(t,s,e,i,n){super(`${t}:${e}:${i}: ${s}`),this.reason=s,this.filename=t,this.line=e,this.column=i,this.source=n}}class s{start;end;source;constructor(t,s,e){this.start=t,this.end=s,this.source=e}}var e;exports.CssTypes=void 0,(e=exports.CssTypes||(exports.CssTypes={})).stylesheet="stylesheet",e.rule="rule",e.declaration="declaration",e.comment="comment",e.container="container",e.charset="charset",e.document="document",e.customMedia="custom-media",e.fontFace="font-face",e.host="host",e.import="import",e.keyframes="keyframes",e.keyframe="keyframe",e.layer="layer",e.media="media",e.namespace="namespace",e.page="page",e.startingStyle="starting-style",e.supports="supports";const i=(t,s,e)=>{let i=e,n=1e4;do{const e=s.map(s=>t.indexOf(s,i));e.push(t.indexOf("\\",i));const r=e.filter(t=>-1!==t);if(0===r.length)return-1;const o=Math.min(...r);if("\\"!==t[o])return o;i=o+2,n--}while(n>0);throw new Error("Too many escaping")},n=(t,s,e)=>{let r=e,o=1e4;do{const e=s.map(s=>t.indexOf(s,r));e.push(t.indexOf("(",r)),e.push(t.indexOf('"',r)),e.push(t.indexOf("'",r)),e.push(t.indexOf("\\",r));const c=e.filter(t=>-1!==t);if(0===c.length)return-1;const a=Math.min(...c);switch(t[a]){case"\\":r=a+2;break;case"(":{const s=n(t,[")"],a+1);if(-1===s)return-1;r=s+1}break;case'"':{const s=i(t,['"'],a+1);if(-1===s)return-1;r=s+1}break;case"'":{const s=i(t,["'"],a+1);if(-1===s)return-1;r=s+1}break;default:return a}o--}while(o>0);throw new Error("Too many escaping")},r=/\/\*[^]*?(?:\*\/|$)/g;function o(t){return t?t.trim():""}function c(t,s){const e=t&&"string"==typeof t.type,i=e?t:s;for(const s in t){const e=t[s];Array.isArray(e)?e.forEach(t=>{c(t,i)}):e&&"object"==typeof e&&c(e,i)}return e&&Object.defineProperty(t,"parent",{configurable:!0,writable:!0,enumerable:!1,value:s||null}),t}class a{level=0;indentation="  ";compress=!1;constructor(t){"string"==typeof t?.indent&&(this.indentation=t?.indent),t?.compress&&(this.compress=!0)}emit(t,s){return t}indent(t){return this.level=this.level||1,t?(this.level+=t,""):Array(this.level).join(this.indentation)}visit(t){switch(t.type){case exports.CssTypes.stylesheet:return this.stylesheet(t);case exports.CssTypes.rule:return this.rule(t);case exports.CssTypes.declaration:return this.declaration(t);case exports.CssTypes.comment:return this.comment(t);case exports.CssTypes.container:return this.container(t);case exports.CssTypes.charset:return this.charset(t);case exports.CssTypes.document:return this.document(t);case exports.CssTypes.customMedia:return this.customMedia(t);case exports.CssTypes.fontFace:return this.fontFace(t);case exports.CssTypes.host:return this.host(t);case exports.CssTypes.import:return this.import(t);case exports.CssTypes.keyframes:return this.keyframes(t);case exports.CssTypes.keyframe:return this.keyframe(t);case exports.CssTypes.layer:return this.layer(t);case exports.CssTypes.media:return this.media(t);case exports.CssTypes.namespace:return this.namespace(t);case exports.CssTypes.page:return this.page(t);case exports.CssTypes.startingStyle:return this.startingStyle(t);case exports.CssTypes.supports:return this.supports(t)}}mapVisit(t,s){let e="";s=s||"";for(let i=0,n=t.length;i<n;i++)e+=this.visit(t[i]),s&&i<n-1&&(e+=this.emit(s));return e}compile(t){return this.compress?t.stylesheet.rules.map(this.visit,this).join(""):this.stylesheet(t)}stylesheet(t){return this.mapVisit(t.stylesheet.rules,"\n\n")}comment(t){return this.compress?this.emit("",t.position):this.emit(`${this.indent()}/*${t.comment}*/`,t.position)}container(t){return this.compress?this.emit(`@container ${t.container}`,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@container ${t.container}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}layer(t){return this.compress?this.emit(`@layer ${t.layer}`,t.position)+(t.rules?this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):";"):this.emit(`${this.indent()}@layer ${t.layer}`,t.position)+(t.rules?this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`):";")}import(t){return this.emit(`@import ${t.import};`,t.position)}media(t){return this.compress?this.emit(`@media ${t.media}`,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@media ${t.media}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}document(t){const s=`@${t.vendor||""}document ${t.document}`;return this.compress?this.emit(s,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(s,t.position)+this.emit(`  {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`${this.indent(-1)}\n}`)}charset(t){return this.emit(`@charset ${t.charset};`,t.position)}namespace(t){return this.emit(`@namespace ${t.namespace};`,t.position)}startingStyle(t){return this.compress?this.emit("@starting-style",t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@starting-style`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}supports(t){return this.compress?this.emit(`@supports ${t.supports}`,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@supports ${t.supports}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}keyframes(t){return this.compress?this.emit(`@${t.vendor||""}keyframes ${t.name}`,t.position)+this.emit("{")+this.mapVisit(t.keyframes)+this.emit("}"):this.emit(`@${t.vendor||""}keyframes ${t.name}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.keyframes,"\n")+this.emit(`${this.indent(-1)}}`)}keyframe(t){const s=t.declarations;return this.compress?this.emit(t.values.join(","),t.position)+this.emit("{")+this.mapVisit(s)+this.emit("}"):this.emit(this.indent())+this.emit(t.values.join(", "),t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(s,"\n")+this.emit(`${this.indent(-1)}\n${this.indent()}}\n`)}page(t){if(this.compress){const s=t.selectors.length?t.selectors.join(", "):"";return this.emit(`@page ${s}`,t.position)+this.emit("{")+this.mapVisit(t.declarations)+this.emit("}")}const s=t.selectors.length?`${t.selectors.join(", ")} `:"";return this.emit(`@page ${s}`,t.position)+this.emit("{\n")+this.emit(this.indent(1))+this.mapVisit(t.declarations,"\n")+this.emit(this.indent(-1))+this.emit("\n}")}fontFace(t){return this.compress?this.emit("@font-face",t.position)+this.emit("{")+this.mapVisit(t.declarations)+this.emit("}"):this.emit("@font-face ",t.position)+this.emit("{\n")+this.emit(this.indent(1))+this.mapVisit(t.declarations,"\n")+this.emit(this.indent(-1))+this.emit("\n}")}host(t){return this.compress?this.emit("@host",t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit("@host",t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`${this.indent(-1)}\n}`)}customMedia(t){return this.emit(`@custom-media ${t.name} ${t.media};`,t.position)}rule(t){const s=t.declarations;if(!s.length)return"";if(this.compress)return this.emit(t.selectors.join(","),t.position)+this.emit("{")+this.mapVisit(s)+this.emit("}");const e=this.indent();return this.emit(t.selectors.map(t=>e+t).join(",\n"),t.position)+this.emit(" {\n")+this.emit(this.indent(1))+this.mapVisit(s,"\n")+this.emit(this.indent(-1))+this.emit(`\n${this.indent()}}`)}declaration(t){return this.compress?this.emit(`${t.property}:${t.value}`,t.position)+this.emit(";"):"grid-template-areas"===t.property?this.emit(this.indent())+this.emit(t.property+": "+t.value.split("\n").join("\n".padEnd(22)+this.indent()),t.position)+this.emit(";"):this.emit(this.indent())+this.emit(`${t.property}: ${t.value}`,t.position)+this.emit(";")}}const p=(e,i)=>{i=i||{};let a=1,p=1;function m(){const t={line:a,column:p};return e=>(e.position=new s(t,{line:a,column:p},i?.source||""),x(),e)}const h=[];function u(s){const n=new t(i?.source||"",s,a,p,e);if(!i?.silent)throw n;h.push(n)}function l(){const t=/^{\s*/.exec(e);return!!t&&(y(t),!0)}function f(){const t=/^}/.exec(e);return!!t&&(y(t),!0)}function d(){let t;const s=[];for(x(),g(s);e.length&&"}"!==e.charAt(0)&&(t=O()||E(),t);)s.push(t),g(s);return s}function y(t){const s=t[0];return function(t){const s=t.match(/\n/g);s&&(a+=s.length);const e=t.lastIndexOf("\n");p=~e?t.length-e:p+t.length}(s),e=e.slice(s.length),t}function x(){const t=/^\s*/.exec(e);t&&y(t)}function g(t){t=t||[];let s=$();for(;s;)t.push(s),s=$();return t}function $(){const t=m();if("/"!==e.charAt(0)||"*"!==e.charAt(1))return;const s=/^\/\*[^]*?\*\//.exec(e);return s?(y(s),t({type:exports.CssTypes.comment,comment:s[0].slice(2,-2)})):u("End of comment missing")}function T(){const t=/^([^{]+)/.exec(e);if(!t)return;y(t);return((t,s)=>{const e=[];let i=0;for(;i<t.length;){const r=n(t,s,i);if(-1===r)return e.push(t.substring(i)),e;e.push(t.substring(i,r)),i=r+1}return e})(o(t[0]).replace(r,""),[","]).map(t=>o(t))}function C(){const t=m(),s=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/.exec(e);if(!s)return;y(s);const i=o(s[0]),c=/^:\s*/.exec(e);if(!c)return u("property missing ':'");y(c);let a="";const p=n(e,[";","}"]);if(-1!==p){a=e.substring(0,p);y([a]),a=o(a).replace(r,"")}const h=t({type:exports.CssTypes.declaration,property:i.replace(r,""),value:a}),l=/^[;\s]*/.exec(e);return l&&y(l),h}function V(){const t=[];if(!l())return u("missing '{'");g(t);let s=C();for(;s;)t.push(s),g(t),s=C();return f()?t:u("missing '}'")}function k(){const t=[],s=m();let i=/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/.exec(e);for(;i;){const s=y(i);t.push(s[1]);const n=/^,\s*/.exec(e);n&&y(n),i=/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/.exec(e)}if(t.length)return s({type:exports.CssTypes.keyframe,values:t,declarations:V()||[]})}const v=j("import"),w=j("charset"),b=j("namespace");function j(t){const s=new RegExp("^@"+t+"\\s*((?::?[^;'\"]|\"(?:\\\\\"|[^\"])*?\"|'(?:\\\\'|[^'])*?')+)(?:;|$)");return()=>{const i=m(),n=s.exec(e);if(!n)return;const r=y(n),o={type:t};return o[t]=r[1].trim(),i(o)}}function O(){if("@"===e[0])return function(){const t=m(),s=/^@([-\w]+)?keyframes\s*/.exec(e);if(!s)return;const i=y(s)[1],n=/^([-\w]+)\s*/.exec(e);if(!n)return u("@keyframes missing name");const r=y(n)[1];if(!l())return u("@keyframes missing '{'");let o=g(),c=k();for(;c;)o.push(c),o=o.concat(g()),c=k();return f()?t({type:exports.CssTypes.keyframes,name:r,vendor:i,keyframes:o}):u("@keyframes missing '}'")}()||function(){const t=m(),s=/^@media *([^{]+)/.exec(e);if(!s)return;const i=o(y(s)[1]);if(!l())return u("@media missing '{'");const n=g().concat(d());return f()?t({type:exports.CssTypes.media,media:i,rules:n}):u("@media missing '}'")}()||function(){const t=m(),s=/^@custom-media\s+(--\S+)\s+([^{;\s][^{;]*);/.exec(e);if(!s)return;const i=y(s);return t({type:exports.CssTypes.customMedia,name:o(i[1]),media:o(i[2])})}()||function(){const t=m(),s=/^@supports *([^{]+)/.exec(e);if(!s)return;const i=o(y(s)[1]);if(!l())return u("@supports missing '{'");const n=g().concat(d());return f()?t({type:exports.CssTypes.supports,supports:i,rules:n}):u("@supports missing '}'")}()||v()||w()||b()||function(){const t=m(),s=/^@([-\w]+)?document *([^{]+)/.exec(e);if(!s)return;const i=y(s),n=o(i[1]),r=o(i[2]);if(!l())return u("@document missing '{'");const c=g().concat(d());return f()?t({type:exports.CssTypes.document,document:r,vendor:n,rules:c}):u("@document missing '}'")}()||function(){const t=m(),s=/^@page */.exec(e);if(!s)return;y(s);const i=T()||[];if(!l())return u("@page missing '{'");let n=g(),r=C();for(;r;)n.push(r),n=n.concat(g()),r=C();return f()?t({type:exports.CssTypes.page,selectors:i,declarations:n}):u("@page missing '}'")}()||function(){const t=m(),s=/^@host\s*/.exec(e);if(!s)return;if(y(s),!l())return u("@host missing '{'");const i=g().concat(d());return f()?t({type:exports.CssTypes.host,rules:i}):u("@host missing '}'")}()||function(){const t=m(),s=/^@font-face\s*/.exec(e);if(!s)return;if(y(s),!l())return u("@font-face missing '{'");let i=g(),n=C();for(;n;)i.push(n),i=i.concat(g()),n=C();return f()?t({type:exports.CssTypes.fontFace,declarations:i}):u("@font-face missing '}'")}()||function(){const t=m(),s=/^@container *([^{]+)/.exec(e);if(!s)return;const i=o(y(s)[1]);if(!l())return u("@container missing '{'");const n=g().concat(d());return f()?t({type:exports.CssTypes.container,container:i,rules:n}):u("@container missing '}'")}()||function(){const t=m(),s=/^@starting-style\s*/.exec(e);if(!s)return;if(y(s),!l())return u("@starting-style missing '{'");const i=g().concat(d());return f()?t({type:exports.CssTypes.startingStyle,rules:i}):u("@starting-style missing '}'")}()||function(){const t=m(),s=/^@layer *([^{;@]+)/.exec(e);if(!s)return;const i=o(y(s)[1]);if(!l()){const s=/^[;\s]*/.exec(e);return s&&y(s),t({type:exports.CssTypes.layer,layer:i})}const n=g().concat(d());return f()?t({type:exports.CssTypes.layer,layer:i,rules:n}):u("@layer missing '}'")}()}function E(){const t=m(),s=T();return s?(g(),t({type:exports.CssTypes.rule,selectors:s,declarations:V()||[]})):u("selector missing")}return c(function(){const t=d();return{type:exports.CssTypes.stylesheet,stylesheet:{source:i?.source,rules:t,parsingErrors:h}}}())},m=(t,s)=>new a(s||{}).compile(t);var h={parse:p,stringify:m};exports.default=h,exports.parse=p,exports.stringify=m;
//# sourceMappingURL=adobe-css-tools.cjs.map
