[{"A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\index.js": "1", "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\App.js": "2", "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\services\\authService.js": "3", "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\pages\\DashboardPage.js": "4", "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\pages\\AuthPage.js": "5", "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\services\\documentService.js": "6", "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\components\\Modal.js": "7", "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\components\\Button.js": "8"}, {"size": 232, "mtime": 1758447945127, "results": "9", "hashOfConfig": "10"}, {"size": 3105, "mtime": 1758447494722, "results": "11", "hashOfConfig": "10"}, {"size": 2949, "mtime": 1758447677341, "results": "12", "hashOfConfig": "10"}, {"size": 9148, "mtime": 1758447559025, "results": "13", "hashOfConfig": "10"}, {"size": 6715, "mtime": 1758447531893, "results": "14", "hashOfConfig": "10"}, {"size": 5787, "mtime": 1758447697635, "results": "15", "hashOfConfig": "10"}, {"size": 1676, "mtime": 1758447513551, "results": "16", "hashOfConfig": "10"}, {"size": 1041, "mtime": 1758447503741, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "b2sw88", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\index.js", [], [], "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\App.js", ["42"], [], "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\services\\authService.js", ["43", "44", "45", "46", "47"], [], "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\pages\\DashboardPage.js", ["48", "49", "50"], [], "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\pages\\AuthPage.js", [], [], "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\services\\documentService.js", ["51", "52", "53", "54", "55", "56", "57", "58", "59", "60"], [], "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\components\\Modal.js", [], [], "A:\\EVERYTHING\\projects\\miniproject\\academic_organizer\\frontend\\src\\components\\Button.js", [], [], {"ruleId": "61", "severity": 1, "message": "62", "line": 28, "column": 9, "nodeType": "63", "messageId": "64", "endLine": 28, "endColumn": 22, "suggestions": "65"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 42, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 42, "endColumn": 20, "suggestions": "66"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 60, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 60, "endColumn": 20, "suggestions": "67"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 76, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 76, "endColumn": 20, "suggestions": "68"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 98, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 98, "endColumn": 20, "suggestions": "69"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 112, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 112, "endColumn": 20, "suggestions": "70"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 27, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 27, "endColumn": 20, "suggestions": "71"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 45, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 45, "endColumn": 20, "suggestions": "72"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 61, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 61, "endColumn": 20, "suggestions": "73"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 46, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 46, "endColumn": 20, "suggestions": "74"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 61, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 61, "endColumn": 20, "suggestions": "75"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 77, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 77, "endColumn": 20, "suggestions": "76"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 93, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 93, "endColumn": 20, "suggestions": "77"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 108, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 108, "endColumn": 20, "suggestions": "78"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 128, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 128, "endColumn": 20, "suggestions": "79"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 143, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 143, "endColumn": 20, "suggestions": "80"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 159, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 159, "endColumn": 20, "suggestions": "81"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 179, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 179, "endColumn": 20, "suggestions": "82"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 194, "column": 7, "nodeType": "63", "messageId": "64", "endLine": 194, "endColumn": 20, "suggestions": "83"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["84"], ["85"], ["86"], ["87"], ["88"], ["89"], ["90"], ["91"], ["92"], ["93"], ["94"], ["95"], ["96"], ["97"], ["98"], ["99"], ["100"], ["101"], ["102"], {"messageId": "103", "data": "104", "fix": "105", "desc": "106"}, {"messageId": "103", "data": "107", "fix": "108", "desc": "106"}, {"messageId": "103", "data": "109", "fix": "110", "desc": "106"}, {"messageId": "103", "data": "111", "fix": "112", "desc": "106"}, {"messageId": "103", "data": "113", "fix": "114", "desc": "106"}, {"messageId": "103", "data": "115", "fix": "116", "desc": "106"}, {"messageId": "103", "data": "117", "fix": "118", "desc": "106"}, {"messageId": "103", "data": "119", "fix": "120", "desc": "106"}, {"messageId": "103", "data": "121", "fix": "122", "desc": "106"}, {"messageId": "103", "data": "123", "fix": "124", "desc": "106"}, {"messageId": "103", "data": "125", "fix": "126", "desc": "106"}, {"messageId": "103", "data": "127", "fix": "128", "desc": "106"}, {"messageId": "103", "data": "129", "fix": "130", "desc": "106"}, {"messageId": "103", "data": "131", "fix": "132", "desc": "106"}, {"messageId": "103", "data": "133", "fix": "134", "desc": "106"}, {"messageId": "103", "data": "135", "fix": "136", "desc": "106"}, {"messageId": "103", "data": "137", "fix": "138", "desc": "106"}, {"messageId": "103", "data": "139", "fix": "140", "desc": "106"}, {"messageId": "103", "data": "141", "fix": "142", "desc": "106"}, "removeConsole", {"propertyName": "143"}, {"range": "144", "text": "145"}, "Remove the console.error().", {"propertyName": "143"}, {"range": "146", "text": "145"}, {"propertyName": "143"}, {"range": "147", "text": "145"}, {"propertyName": "143"}, {"range": "148", "text": "145"}, {"propertyName": "143"}, {"range": "149", "text": "145"}, {"propertyName": "143"}, {"range": "150", "text": "145"}, {"propertyName": "143"}, {"range": "151", "text": "145"}, {"propertyName": "143"}, {"range": "152", "text": "145"}, {"propertyName": "143"}, {"range": "153", "text": "145"}, {"propertyName": "143"}, {"range": "154", "text": "145"}, {"propertyName": "143"}, {"range": "155", "text": "145"}, {"propertyName": "143"}, {"range": "156", "text": "145"}, {"propertyName": "143"}, {"range": "157", "text": "145"}, {"propertyName": "143"}, {"range": "158", "text": "145"}, {"propertyName": "143"}, {"range": "159", "text": "145"}, {"propertyName": "143"}, {"range": "160", "text": "145"}, {"propertyName": "143"}, {"range": "161", "text": "145"}, {"propertyName": "143"}, {"range": "162", "text": "145"}, {"propertyName": "143"}, {"range": "163", "text": "145"}, "error", [1003, 1046], "", [1050, 1094], [1491, 1528], [1859, 1902], [2442, 2490], [2783, 2828], [981, 1030], [1492, 1530], [1934, 1972], [1111, 1156], [1491, 1535], [1916, 1963], [2350, 2397], [2726, 2773], [3214, 3262], [3580, 3626], [4000, 4047], [4508, 4557], [4873, 4923]]