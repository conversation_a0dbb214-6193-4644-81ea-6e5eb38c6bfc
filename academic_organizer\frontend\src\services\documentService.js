// Document service for API interactions
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

class DocumentService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/documents`;
  }

  // Get authorization headers
  getAuthHeaders() {
    const token = localStorage.getItem('access_token');
    return {
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  // Get authorization headers for JSON requests
  getJsonAuthHeaders() {
    return {
      'Content-Type': 'application/json',
      ...this.getAuthHeaders()
    };
  }

  // Handle API responses
  async handleResponse(response) {
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }
    
    return data;
  }

  // Get all documents for the current user
  async getDocuments() {
    try {
      const response = await fetch(this.baseURL, {
        method: 'GET',
        headers: this.getJsonAuthHeaders()
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Get documents error:', error);
      throw error;
    }
  }

  // Get a specific document by ID
  async getDocument(documentId) {
    try {
      const response = await fetch(`${this.baseURL}/${documentId}`, {
        method: 'GET',
        headers: this.getJsonAuthHeaders()
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Get document error:', error);
      throw error;
    }
  }

  // Upload a new document
  async uploadDocument(formData) {
    try {
      const response = await fetch(`${this.baseURL}/upload`, {
        method: 'POST',
        headers: this.getAuthHeaders(), // Don't set Content-Type for FormData
        body: formData
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Upload document error:', error);
      throw error;
    }
  }

  // Update document metadata
  async updateDocument(documentId, updateData) {
    try {
      const response = await fetch(`${this.baseURL}/${documentId}`, {
        method: 'PUT',
        headers: this.getJsonAuthHeaders(),
        body: JSON.stringify(updateData)
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Update document error:', error);
      throw error;
    }
  }

  // Delete a document
  async deleteDocument(documentId) {
    try {
      const response = await fetch(`${this.baseURL}/${documentId}`, {
        method: 'DELETE',
        headers: this.getJsonAuthHeaders()
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Delete document error:', error);
      throw error;
    }
  }

  // Search documents
  async searchDocuments(query, filters = {}) {
    try {
      const searchParams = new URLSearchParams({
        q: query,
        ...filters
      });

      const response = await fetch(`${this.baseURL}/search?${searchParams}`, {
        method: 'GET',
        headers: this.getJsonAuthHeaders()
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Search documents error:', error);
      throw error;
    }
  }

  // Get document categories
  async getCategories() {
    try {
      const response = await fetch(`${this.baseURL}/categories`, {
        method: 'GET',
        headers: this.getJsonAuthHeaders()
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Get categories error:', error);
      throw error;
    }
  }

  // Create a new category
  async createCategory(categoryData) {
    try {
      const response = await fetch(`${this.baseURL}/categories`, {
        method: 'POST',
        headers: this.getJsonAuthHeaders(),
        body: JSON.stringify(categoryData)
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Create category error:', error);
      throw error;
    }
  }

  // Download a document
  async downloadDocument(documentId) {
    try {
      const response = await fetch(`${this.baseURL}/${documentId}/download`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Return the blob for download
      return await response.blob();
    } catch (error) {
      console.error('Download document error:', error);
      throw error;
    }
  }

  // Get document statistics
  async getDocumentStats() {
    try {
      const response = await fetch(`${this.baseURL}/stats`, {
        method: 'GET',
        headers: this.getJsonAuthHeaders()
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Get document stats error:', error);
      throw error;
    }
  }

  // Utility method to format file size
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Utility method to get file type icon
  getFileTypeIcon(fileType) {
    const iconMap = {
      pdf: '📄',
      doc: '📝',
      docx: '📝',
      txt: '📄',
      png: '🖼️',
      jpg: '🖼️',
      jpeg: '🖼️',
      gif: '🖼️',
      ppt: '📊',
      pptx: '📊',
      xls: '📈',
      xlsx: '📈'
    };
    
    return iconMap[fileType.toLowerCase()] || '📎';
  }
}

// Create and export a singleton instance
export const documentService = new DocumentService();
