{"ast": null, "code": "// Document service for API interactions\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\nclass DocumentService {\n  constructor() {\n    this.baseURL = `${API_BASE_URL}/documents`;\n  }\n\n  // Get authorization headers\n  getAuthHeaders() {\n    const token = localStorage.getItem('access_token');\n    return {\n      ...(token && {\n        'Authorization': `Bearer ${token}`\n      })\n    };\n  }\n\n  // Get authorization headers for JSON requests\n  getJsonAuthHeaders() {\n    return {\n      'Content-Type': 'application/json',\n      ...this.getAuthHeaders()\n    };\n  }\n\n  // Handle API responses\n  async handleResponse(response) {\n    const data = await response.json();\n    if (!response.ok) {\n      throw new Error(data.error || `HTTP error! status: ${response.status}`);\n    }\n    return data;\n  }\n\n  // Get all documents for the current user\n  async getDocuments() {\n    try {\n      const response = await fetch(this.baseURL, {\n        method: 'GET',\n        headers: this.getJsonAuthHeaders()\n      });\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Get documents error:', error);\n      throw error;\n    }\n  }\n\n  // Get a specific document by ID\n  async getDocument(documentId) {\n    try {\n      const response = await fetch(`${this.baseURL}/${documentId}`, {\n        method: 'GET',\n        headers: this.getJsonAuthHeaders()\n      });\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Get document error:', error);\n      throw error;\n    }\n  }\n\n  // Upload a new document\n  async uploadDocument(formData) {\n    try {\n      const response = await fetch(`${this.baseURL}/upload`, {\n        method: 'POST',\n        headers: this.getAuthHeaders(),\n        // Don't set Content-Type for FormData\n        body: formData\n      });\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Upload document error:', error);\n      throw error;\n    }\n  }\n\n  // Update document metadata\n  async updateDocument(documentId, updateData) {\n    try {\n      const response = await fetch(`${this.baseURL}/${documentId}`, {\n        method: 'PUT',\n        headers: this.getJsonAuthHeaders(),\n        body: JSON.stringify(updateData)\n      });\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Update document error:', error);\n      throw error;\n    }\n  }\n\n  // Delete a document\n  async deleteDocument(documentId) {\n    try {\n      const response = await fetch(`${this.baseURL}/${documentId}`, {\n        method: 'DELETE',\n        headers: this.getJsonAuthHeaders()\n      });\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Delete document error:', error);\n      throw error;\n    }\n  }\n\n  // Search documents\n  async searchDocuments(query, filters = {}) {\n    try {\n      const searchParams = new URLSearchParams({\n        q: query,\n        ...filters\n      });\n      const response = await fetch(`${this.baseURL}/search?${searchParams}`, {\n        method: 'GET',\n        headers: this.getJsonAuthHeaders()\n      });\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Search documents error:', error);\n      throw error;\n    }\n  }\n\n  // Get document categories\n  async getCategories() {\n    try {\n      const response = await fetch(`${this.baseURL}/categories`, {\n        method: 'GET',\n        headers: this.getJsonAuthHeaders()\n      });\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Get categories error:', error);\n      throw error;\n    }\n  }\n\n  // Create a new category\n  async createCategory(categoryData) {\n    try {\n      const response = await fetch(`${this.baseURL}/categories`, {\n        method: 'POST',\n        headers: this.getJsonAuthHeaders(),\n        body: JSON.stringify(categoryData)\n      });\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Create category error:', error);\n      throw error;\n    }\n  }\n\n  // Download a document\n  async downloadDocument(documentId) {\n    try {\n      const response = await fetch(`${this.baseURL}/${documentId}/download`, {\n        method: 'GET',\n        headers: this.getAuthHeaders()\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      // Return the blob for download\n      return await response.blob();\n    } catch (error) {\n      console.error('Download document error:', error);\n      throw error;\n    }\n  }\n\n  // Get document statistics\n  async getDocumentStats() {\n    try {\n      const response = await fetch(`${this.baseURL}/stats`, {\n        method: 'GET',\n        headers: this.getJsonAuthHeaders()\n      });\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Get document stats error:', error);\n      throw error;\n    }\n  }\n\n  // Utility method to format file size\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  // Utility method to get file type icon\n  getFileTypeIcon(fileType) {\n    const iconMap = {\n      pdf: '📄',\n      doc: '📝',\n      docx: '📝',\n      txt: '📄',\n      png: '🖼️',\n      jpg: '🖼️',\n      jpeg: '🖼️',\n      gif: '🖼️',\n      ppt: '📊',\n      pptx: '📊',\n      xls: '📈',\n      xlsx: '📈'\n    };\n    return iconMap[fileType.toLowerCase()] || '📎';\n  }\n}\n\n// Create and export a singleton instance\nexport const documentService = new DocumentService();", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "DocumentService", "constructor", "baseURL", "getAuthHeaders", "token", "localStorage", "getItem", "getJsonAuthHeaders", "handleResponse", "response", "data", "json", "ok", "Error", "error", "status", "getDocuments", "fetch", "method", "headers", "console", "getDocument", "documentId", "uploadDocument", "formData", "body", "updateDocument", "updateData", "JSON", "stringify", "deleteDocument", "searchDocuments", "query", "filters", "searchParams", "URLSearchParams", "q", "getCategories", "createCategory", "categoryData", "downloadDocument", "blob", "getDocumentStats", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "getFileTypeIcon", "fileType", "iconMap", "pdf", "doc", "docx", "txt", "png", "jpg", "jpeg", "gif", "ppt", "pptx", "xls", "xlsx", "toLowerCase", "documentService"], "sources": ["A:/EVERYTHING/projects/miniproject/academic_organizer/frontend/src/services/documentService.js"], "sourcesContent": ["// Document service for API interactions\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\nclass DocumentService {\n  constructor() {\n    this.baseURL = `${API_BASE_URL}/documents`;\n  }\n\n  // Get authorization headers\n  getAuthHeaders() {\n    const token = localStorage.getItem('access_token');\n    return {\n      ...(token && { 'Authorization': `Bearer ${token}` })\n    };\n  }\n\n  // Get authorization headers for JSON requests\n  getJsonAuthHeaders() {\n    return {\n      'Content-Type': 'application/json',\n      ...this.getAuthHeaders()\n    };\n  }\n\n  // Handle API responses\n  async handleResponse(response) {\n    const data = await response.json();\n    \n    if (!response.ok) {\n      throw new Error(data.error || `HTTP error! status: ${response.status}`);\n    }\n    \n    return data;\n  }\n\n  // Get all documents for the current user\n  async getDocuments() {\n    try {\n      const response = await fetch(this.baseURL, {\n        method: 'GET',\n        headers: this.getJsonAuthHeaders()\n      });\n\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Get documents error:', error);\n      throw error;\n    }\n  }\n\n  // Get a specific document by ID\n  async getDocument(documentId) {\n    try {\n      const response = await fetch(`${this.baseURL}/${documentId}`, {\n        method: 'GET',\n        headers: this.getJsonAuthHeaders()\n      });\n\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Get document error:', error);\n      throw error;\n    }\n  }\n\n  // Upload a new document\n  async uploadDocument(formData) {\n    try {\n      const response = await fetch(`${this.baseURL}/upload`, {\n        method: 'POST',\n        headers: this.getAuthHeaders(), // Don't set Content-Type for FormData\n        body: formData\n      });\n\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Upload document error:', error);\n      throw error;\n    }\n  }\n\n  // Update document metadata\n  async updateDocument(documentId, updateData) {\n    try {\n      const response = await fetch(`${this.baseURL}/${documentId}`, {\n        method: 'PUT',\n        headers: this.getJsonAuthHeaders(),\n        body: JSON.stringify(updateData)\n      });\n\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Update document error:', error);\n      throw error;\n    }\n  }\n\n  // Delete a document\n  async deleteDocument(documentId) {\n    try {\n      const response = await fetch(`${this.baseURL}/${documentId}`, {\n        method: 'DELETE',\n        headers: this.getJsonAuthHeaders()\n      });\n\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Delete document error:', error);\n      throw error;\n    }\n  }\n\n  // Search documents\n  async searchDocuments(query, filters = {}) {\n    try {\n      const searchParams = new URLSearchParams({\n        q: query,\n        ...filters\n      });\n\n      const response = await fetch(`${this.baseURL}/search?${searchParams}`, {\n        method: 'GET',\n        headers: this.getJsonAuthHeaders()\n      });\n\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Search documents error:', error);\n      throw error;\n    }\n  }\n\n  // Get document categories\n  async getCategories() {\n    try {\n      const response = await fetch(`${this.baseURL}/categories`, {\n        method: 'GET',\n        headers: this.getJsonAuthHeaders()\n      });\n\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Get categories error:', error);\n      throw error;\n    }\n  }\n\n  // Create a new category\n  async createCategory(categoryData) {\n    try {\n      const response = await fetch(`${this.baseURL}/categories`, {\n        method: 'POST',\n        headers: this.getJsonAuthHeaders(),\n        body: JSON.stringify(categoryData)\n      });\n\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Create category error:', error);\n      throw error;\n    }\n  }\n\n  // Download a document\n  async downloadDocument(documentId) {\n    try {\n      const response = await fetch(`${this.baseURL}/${documentId}/download`, {\n        method: 'GET',\n        headers: this.getAuthHeaders()\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      // Return the blob for download\n      return await response.blob();\n    } catch (error) {\n      console.error('Download document error:', error);\n      throw error;\n    }\n  }\n\n  // Get document statistics\n  async getDocumentStats() {\n    try {\n      const response = await fetch(`${this.baseURL}/stats`, {\n        method: 'GET',\n        headers: this.getJsonAuthHeaders()\n      });\n\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Get document stats error:', error);\n      throw error;\n    }\n  }\n\n  // Utility method to format file size\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    \n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    \n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  // Utility method to get file type icon\n  getFileTypeIcon(fileType) {\n    const iconMap = {\n      pdf: '📄',\n      doc: '📝',\n      docx: '📝',\n      txt: '📄',\n      png: '🖼️',\n      jpg: '🖼️',\n      jpeg: '🖼️',\n      gif: '🖼️',\n      ppt: '📊',\n      pptx: '📊',\n      xls: '📈',\n      xlsx: '📈'\n    };\n    \n    return iconMap[fileType.toLowerCase()] || '📎';\n  }\n}\n\n// Create and export a singleton instance\nexport const documentService = new DocumentService();\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,eAAe,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,GAAGN,YAAY,YAAY;EAC5C;;EAEA;EACAO,cAAcA,CAAA,EAAG;IACf,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAClD,OAAO;MACL,IAAIF,KAAK,IAAI;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAG,CAAC;IACrD,CAAC;EACH;;EAEA;EACAG,kBAAkBA,CAAA,EAAG;IACnB,OAAO;MACL,cAAc,EAAE,kBAAkB;MAClC,GAAG,IAAI,CAACJ,cAAc,CAAC;IACzB,CAAC;EACH;;EAEA;EACA,MAAMK,cAAcA,CAACC,QAAQ,EAAE;IAC7B,MAAMC,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;IAElC,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,KAAK,IAAI,uBAAuBL,QAAQ,CAACM,MAAM,EAAE,CAAC;IACzE;IAEA,OAAOL,IAAI;EACb;;EAEA;EACA,MAAMM,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMQ,KAAK,CAAC,IAAI,CAACf,OAAO,EAAE;QACzCgB,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,IAAI,CAACZ,kBAAkB,CAAC;MACnC,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAACC,cAAc,CAACC,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMO,WAAWA,CAACC,UAAU,EAAE;IAC5B,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMQ,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,IAAIoB,UAAU,EAAE,EAAE;QAC5DJ,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,IAAI,CAACZ,kBAAkB,CAAC;MACnC,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAACC,cAAc,CAACC,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMS,cAAcA,CAACC,QAAQ,EAAE;IAC7B,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMQ,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,SAAS,EAAE;QACrDgB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,IAAI,CAAChB,cAAc,CAAC,CAAC;QAAE;QAChCsB,IAAI,EAAED;MACR,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAAChB,cAAc,CAACC,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMY,cAAcA,CAACJ,UAAU,EAAEK,UAAU,EAAE;IAC3C,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMQ,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,IAAIoB,UAAU,EAAE,EAAE;QAC5DJ,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,IAAI,CAACZ,kBAAkB,CAAC,CAAC;QAClCkB,IAAI,EAAEG,IAAI,CAACC,SAAS,CAACF,UAAU;MACjC,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAACnB,cAAc,CAACC,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMgB,cAAcA,CAACR,UAAU,EAAE;IAC/B,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMQ,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,IAAIoB,UAAU,EAAE,EAAE;QAC5DJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,IAAI,CAACZ,kBAAkB,CAAC;MACnC,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAACC,cAAc,CAACC,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMiB,eAAeA,CAACC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACzC,IAAI;MACF,MAAMC,YAAY,GAAG,IAAIC,eAAe,CAAC;QACvCC,CAAC,EAAEJ,KAAK;QACR,GAAGC;MACL,CAAC,CAAC;MAEF,MAAMxB,QAAQ,GAAG,MAAMQ,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,WAAWgC,YAAY,EAAE,EAAE;QACrEhB,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,IAAI,CAACZ,kBAAkB,CAAC;MACnC,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAACC,cAAc,CAACC,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMuB,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAM5B,QAAQ,GAAG,MAAMQ,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,aAAa,EAAE;QACzDgB,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,IAAI,CAACZ,kBAAkB,CAAC;MACnC,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAACC,cAAc,CAACC,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMwB,cAAcA,CAACC,YAAY,EAAE;IACjC,IAAI;MACF,MAAM9B,QAAQ,GAAG,MAAMQ,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,aAAa,EAAE;QACzDgB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,IAAI,CAACZ,kBAAkB,CAAC,CAAC;QAClCkB,IAAI,EAAEG,IAAI,CAACC,SAAS,CAACU,YAAY;MACnC,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAAC/B,cAAc,CAACC,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAM0B,gBAAgBA,CAAClB,UAAU,EAAE;IACjC,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMQ,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,IAAIoB,UAAU,WAAW,EAAE;QACrEJ,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,IAAI,CAAChB,cAAc,CAAC;MAC/B,CAAC,CAAC;MAEF,IAAI,CAACM,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBJ,QAAQ,CAACM,MAAM,EAAE,CAAC;MAC3D;;MAEA;MACA,OAAO,MAAMN,QAAQ,CAACgC,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAM4B,gBAAgBA,CAAA,EAAG;IACvB,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMQ,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,QAAQ,EAAE;QACpDgB,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,IAAI,CAACZ,kBAAkB,CAAC;MACnC,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAACC,cAAc,CAACC,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF;;EAEA;EACA6B,cAAcA,CAACC,KAAK,EAAE;IACpB,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IAEjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/C,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IAEnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE;;EAEA;EACAO,eAAeA,CAACC,QAAQ,EAAE;IACxB,MAAMC,OAAO,GAAG;MACdC,GAAG,EAAE,IAAI;MACTC,GAAG,EAAE,IAAI;MACTC,IAAI,EAAE,IAAI;MACVC,GAAG,EAAE,IAAI;MACTC,GAAG,EAAE,KAAK;MACVC,GAAG,EAAE,KAAK;MACVC,IAAI,EAAE,KAAK;MACXC,GAAG,EAAE,KAAK;MACVC,GAAG,EAAE,IAAI;MACTC,IAAI,EAAE,IAAI;MACVC,GAAG,EAAE,IAAI;MACTC,IAAI,EAAE;IACR,CAAC;IAED,OAAOZ,OAAO,CAACD,QAAQ,CAACc,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI;EAChD;AACF;;AAEA;AACA,OAAO,MAAMC,eAAe,GAAG,IAAItE,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}