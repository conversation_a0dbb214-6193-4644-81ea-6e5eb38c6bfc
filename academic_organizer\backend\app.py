# Flask main application
from flask import Flask
from flask_cors import CORS
from config import Config

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Enable CORS for frontend communication
    CORS(app)
    
    # Register blueprints
    from routes.auth_routes import auth_bp
    from routes.document_routes import document_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(document_bp, url_prefix='/api/documents')
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True)
