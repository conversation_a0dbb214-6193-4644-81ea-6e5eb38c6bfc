#!/usr/bin/env python3
"""
Start script for Academic Organizer Flask server
"""

from app import create_app
import sys

def main():
    print("🚀 Starting Academic Organizer API server...")
    print("📍 Server will be available at: http://localhost:5000")
    print("📍 API endpoints at: http://localhost:5000/api")
    print("🔧 Debug mode: ON")
    print("⏹️  Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        app = create_app()
        print("✅ App created successfully")
        print("🌐 Starting Flask development server...")
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
