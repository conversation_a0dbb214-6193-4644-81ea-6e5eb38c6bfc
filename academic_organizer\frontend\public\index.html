<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Academic Organizer</title>
    <meta name="description" content="Organize your academic documents and resources efficiently">
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- Preload critical fonts -->
    <link rel="preload" href="/assets/fonts/inter-regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/assets/fonts/inter-medium.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- Critical CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
        }
        
        #root {
            min-height: 100vh;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 1.2rem;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">Loading Academic Organizer...</div>
    </div>
    
    <!-- React/Vue/Svelte app will be mounted here -->
    <script src="/src/main.js" type="module"></script>
</body>
</html>
