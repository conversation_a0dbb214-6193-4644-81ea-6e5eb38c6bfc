{"name": "academic-organizer-frontend", "version": "1.0.0", "description": "Frontend for Academic Organizer - A document management system for academic resources", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "web-vitals": "^3.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx", "lint:fix": "eslint src --ext .js,.jsx --fix", "format": "prettier --write src/**/*.{js,jsx,css,md}", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-unused-vars": "warn", "no-console": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000", "homepage": ".", "keywords": ["academic", "organizer", "document-management", "react", "education"], "author": "Academic Organizer Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/academic-organizer.git"}, "bugs": {"url": "https://github.com/your-username/academic-organizer/issues"}}