// Dashboard page component
import React, { useState, useEffect } from 'react';
import Button from '../components/Button';
import Modal from '../components/Modal';
import { documentService } from '../services/documentService';

const DashboardPage = ({ user, onLogout, theme, onToggleTheme }) => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const response = await documentService.getDocuments();
      setDocuments(response.documents);
    } catch (error) {
      setError('Failed to load documents');
      console.error('Error loading documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (event) => {
    event.preventDefault();
    const formData = new FormData(event.target);
    
    try {
      setUploadLoading(true);
      await documentService.uploadDocument(formData);
      setShowUploadModal(false);
      loadDocuments(); // Refresh the document list
      event.target.reset();
    } catch (error) {
      setError('Failed to upload document');
      console.error('Upload error:', error);
    } finally {
      setUploadLoading(false);
    }
  };

  const handleDeleteDocument = async (documentId) => {
    if (!window.confirm('Are you sure you want to delete this document?')) {
      return;
    }

    try {
      await documentService.deleteDocument(documentId);
      loadDocuments(); // Refresh the document list
    } catch (error) {
      setError('Failed to delete document');
      console.error('Delete error:', error);
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = [...new Set(documents.map(doc => doc.category).filter(Boolean))];

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <div className="header-left">
          <h1>Academic Organizer</h1>
          <p>Welcome back, {user?.first_name}!</p>
        </div>
        <div className="header-right">
          <Button
            variant="ghost"
            size="small"
            onClick={onToggleTheme}
            className="theme-toggle"
          >
            {theme === 'light' ? '🌙' : '☀️'}
          </Button>
          <Button
            variant="secondary"
            size="small"
            onClick={onLogout}
          >
            Logout
          </Button>
        </div>
      </header>

      <div className="dashboard-content">
        <div className="dashboard-controls">
          <div className="search-filter-section">
            <div className="search-box">
              <input
                type="text"
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
            <div className="filter-section">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="category-filter"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
          <Button
            variant="primary"
            onClick={() => setShowUploadModal(true)}
          >
            Upload Document
          </Button>
        </div>

        {error && <div className="error-message">{error}</div>}

        <div className="documents-section">
          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading documents...</p>
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="empty-state">
              <h3>No documents found</h3>
              <p>
                {searchTerm || selectedCategory !== 'all' 
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Upload your first document to get started!'
                }
              </p>
            </div>
          ) : (
            <div className="documents-grid">
              {filteredDocuments.map(document => (
                <div key={document.id} className="document-card">
                  <div className="document-header">
                    <h3 className="document-title">{document.title}</h3>
                    <div className="document-actions">
                      <Button
                        variant="ghost"
                        size="small"
                        onClick={() => handleDeleteDocument(document.id)}
                        className="delete-btn"
                      >
                        🗑️
                      </Button>
                    </div>
                  </div>
                  <p className="document-description">{document.description}</p>
                  <div className="document-meta">
                    <span className="document-type">{document.file_type.toUpperCase()}</span>
                    <span className="document-size">
                      {(document.file_size / 1024).toFixed(1)} KB
                    </span>
                    {document.category && (
                      <span className="document-category">{document.category}</span>
                    )}
                  </div>
                  <div className="document-tags">
                    {document.tags.map((tag, index) => (
                      <span key={index} className="tag">{tag}</span>
                    ))}
                  </div>
                  <div className="document-date">
                    Uploaded: {new Date(document.created_at).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <Modal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        title="Upload Document"
        size="medium"
      >
        <form onSubmit={handleFileUpload} className="upload-form">
          <div className="form-group">
            <label htmlFor="file">Select File</label>
            <input
              type="file"
              id="file"
              name="file"
              required
              className="form-input"
              accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.gif,.ppt,.pptx,.xls,.xlsx"
            />
          </div>
          <div className="form-group">
            <label htmlFor="title">Title</label>
            <input
              type="text"
              id="title"
              name="title"
              className="form-input"
              placeholder="Document title (optional)"
            />
          </div>
          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              className="form-input"
              placeholder="Document description (optional)"
              rows="3"
            />
          </div>
          <div className="form-group">
            <label htmlFor="category">Category</label>
            <input
              type="text"
              id="category"
              name="category"
              className="form-input"
              placeholder="e.g., Research, Assignments, Notes"
            />
          </div>
          <div className="form-group">
            <label htmlFor="tags">Tags (JSON format)</label>
            <input
              type="text"
              id="tags"
              name="tags"
              className="form-input"
              placeholder='["tag1", "tag2", "tag3"]'
              defaultValue="[]"
            />
          </div>
          <div className="modal-actions">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setShowUploadModal(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={uploadLoading}
            >
              Upload
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default DashboardPage;
