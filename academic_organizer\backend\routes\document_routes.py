# Document management routes
from flask import Blueprint, request, jsonify, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
import os
import json
from models import db, Document, User
from config import Config

document_bp = Blueprint('documents', __name__)

ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@document_bp.route('/', methods=['GET'])
@jwt_required()
def get_documents():
    """Get all documents for the current user"""
    try:
        user_id = get_jwt_identity()
        documents = Document.query.filter_by(user_id=user_id).all()
        
        return jsonify({
            'documents': [{
                'id': doc.id,
                'title': doc.title,
                'description': doc.description,
                'file_type': doc.file_type,
                'file_size': doc.file_size,
                'tags': json.loads(doc.tags) if doc.tags else [],
                'category': doc.category,
                'created_at': doc.created_at.isoformat(),
                'updated_at': doc.updated_at.isoformat()
            } for doc in documents]
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@document_bp.route('/upload', methods=['POST'])
@jwt_required()
def upload_document():
    """Upload a new document"""
    try:
        user_id = get_jwt_identity()
        
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            
            # Create upload directory if it doesn't exist
            upload_dir = Config.UPLOAD_FOLDER
            if not os.path.exists(upload_dir):
                os.makedirs(upload_dir)
            
            # Save file
            file_path = os.path.join(upload_dir, filename)
            file.save(file_path)
            
            # Get form data
            title = request.form.get('title', filename)
            description = request.form.get('description', '')
            category = request.form.get('category', '')
            tags = request.form.get('tags', '[]')
            
            # Create document record
            document = Document(
                title=title,
                description=description,
                file_path=file_path,
                file_type=filename.rsplit('.', 1)[1].lower(),
                file_size=os.path.getsize(file_path),
                tags=tags,
                category=category,
                user_id=user_id
            )
            
            db.session.add(document)
            db.session.commit()
            
            return jsonify({
                'message': 'Document uploaded successfully',
                'document': {
                    'id': document.id,
                    'title': document.title,
                    'description': document.description,
                    'file_type': document.file_type,
                    'file_size': document.file_size,
                    'tags': json.loads(document.tags) if document.tags else [],
                    'category': document.category,
                    'created_at': document.created_at.isoformat()
                }
            }), 201
        else:
            return jsonify({'error': 'File type not allowed'}), 400
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@document_bp.route('/<document_id>', methods=['GET'])
@jwt_required()
def get_document(document_id):
    """Get a specific document"""
    try:
        user_id = get_jwt_identity()
        document = Document.query.filter_by(id=document_id, user_id=user_id).first()
        
        if not document:
            return jsonify({'error': 'Document not found'}), 404
        
        return jsonify({
            'document': {
                'id': document.id,
                'title': document.title,
                'description': document.description,
                'file_type': document.file_type,
                'file_size': document.file_size,
                'tags': json.loads(document.tags) if document.tags else [],
                'category': document.category,
                'created_at': document.created_at.isoformat(),
                'updated_at': document.updated_at.isoformat()
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@document_bp.route('/<document_id>', methods=['DELETE'])
@jwt_required()
def delete_document(document_id):
    """Delete a document"""
    try:
        user_id = get_jwt_identity()
        document = Document.query.filter_by(id=document_id, user_id=user_id).first()
        
        if not document:
            return jsonify({'error': 'Document not found'}), 404
        
        # Delete file from filesystem
        if os.path.exists(document.file_path):
            os.remove(document.file_path)
        
        # Delete from database
        db.session.delete(document)
        db.session.commit()
        
        return jsonify({'message': 'Document deleted successfully'}), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
