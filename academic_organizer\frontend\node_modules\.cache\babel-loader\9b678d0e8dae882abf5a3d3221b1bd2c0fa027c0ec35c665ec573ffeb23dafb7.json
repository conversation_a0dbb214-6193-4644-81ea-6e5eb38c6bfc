{"ast": null, "code": "var _jsxFileName = \"A:\\\\EVERYTHING\\\\projects\\\\miniproject\\\\academic_organizer\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\n// Main React application component\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport AuthPage from './pages/AuthPage';\nimport DashboardPage from './pages/DashboardPage';\nimport { authService } from './services/authService';\nimport './styles/base.css';\nimport './styles/themes.css';\nimport './styles/animations.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [user, setUser] = useState(null);\n  const [theme, setTheme] = useState('light');\n  useEffect(() => {\n    // Check if user is already authenticated\n    const checkAuth = async () => {\n      try {\n        const token = localStorage.getItem('access_token');\n        if (token) {\n          const userProfile = await authService.getProfile();\n          setUser(userProfile);\n          setIsAuthenticated(true);\n        }\n      } catch (error) {\n        console.error('Auth check failed:', error);\n        localStorage.removeItem('access_token');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    checkAuth();\n\n    // Load theme preference\n    const savedTheme = localStorage.getItem('theme') || 'light';\n    setTheme(savedTheme);\n    document.documentElement.setAttribute('data-theme', savedTheme);\n  }, []);\n  const handleLogin = (userData, token) => {\n    localStorage.setItem('access_token', token);\n    setUser(userData);\n    setIsAuthenticated(true);\n  };\n  const handleLogout = () => {\n    localStorage.removeItem('access_token');\n    setUser(null);\n    setIsAuthenticated(false);\n  };\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n    localStorage.setItem('theme', newTheme);\n    document.documentElement.setAttribute('data-theme', newTheme);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading Academic Organizer...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app\",\n      \"data-theme\": theme,\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth\",\n          element: !isAuthenticated ? /*#__PURE__*/_jsxDEV(AuthPage, {\n            onLogin: handleLogin\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: isAuthenticated ? /*#__PURE__*/_jsxDEV(DashboardPage, {\n            user: user,\n            onLogout: handleLogout,\n            theme: theme,\n            onToggleTheme: toggleTheme\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/auth\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: isAuthenticated ? \"/dashboard\" : \"/auth\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"uEyz/dMsi1zUeHnZz20KEaQzRdo=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "AuthPage", "DashboardPage", "authService", "jsxDEV", "_jsxDEV", "App", "_s", "isAuthenticated", "setIsAuthenticated", "isLoading", "setIsLoading", "user", "setUser", "theme", "setTheme", "checkAuth", "token", "localStorage", "getItem", "userProfile", "getProfile", "error", "console", "removeItem", "savedTheme", "document", "documentElement", "setAttribute", "handleLogin", "userData", "setItem", "handleLogout", "toggleTheme", "newTheme", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "onLogin", "to", "replace", "onLogout", "onToggleTheme", "_c", "$RefreshReg$"], "sources": ["A:/EVERYTHING/projects/miniproject/academic_organizer/frontend/src/App.js"], "sourcesContent": ["// Main React application component\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport AuthPage from './pages/AuthPage';\nimport DashboardPage from './pages/DashboardPage';\nimport { authService } from './services/authService';\nimport './styles/base.css';\nimport './styles/themes.css';\nimport './styles/animations.css';\n\nfunction App() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [user, setUser] = useState(null);\n  const [theme, setTheme] = useState('light');\n\n  useEffect(() => {\n    // Check if user is already authenticated\n    const checkAuth = async () => {\n      try {\n        const token = localStorage.getItem('access_token');\n        if (token) {\n          const userProfile = await authService.getProfile();\n          setUser(userProfile);\n          setIsAuthenticated(true);\n        }\n      } catch (error) {\n        console.error('Auth check failed:', error);\n        localStorage.removeItem('access_token');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    checkAuth();\n\n    // Load theme preference\n    const savedTheme = localStorage.getItem('theme') || 'light';\n    setTheme(savedTheme);\n    document.documentElement.setAttribute('data-theme', savedTheme);\n  }, []);\n\n  const handleLogin = (userData, token) => {\n    localStorage.setItem('access_token', token);\n    setUser(userData);\n    setIsAuthenticated(true);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('access_token');\n    setUser(null);\n    setIsAuthenticated(false);\n  };\n\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n    localStorage.setItem('theme', newTheme);\n    document.documentElement.setAttribute('data-theme', newTheme);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"loading-container\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading Academic Organizer...</p>\n      </div>\n    );\n  }\n\n  return (\n    <Router>\n      <div className=\"app\" data-theme={theme}>\n        <Routes>\n          <Route \n            path=\"/auth\" \n            element={\n              !isAuthenticated ? (\n                <AuthPage onLogin={handleLogin} />\n              ) : (\n                <Navigate to=\"/dashboard\" replace />\n              )\n            } \n          />\n          <Route \n            path=\"/dashboard\" \n            element={\n              isAuthenticated ? (\n                <DashboardPage \n                  user={user} \n                  onLogout={handleLogout}\n                  theme={theme}\n                  onToggleTheme={toggleTheme}\n                />\n              ) : (\n                <Navigate to=\"/auth\" replace />\n              )\n            } \n          />\n          <Route \n            path=\"/\" \n            element={\n              <Navigate to={isAuthenticated ? \"/dashboard\" : \"/auth\"} replace />\n            } \n          />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,mBAAmB;AAC1B,OAAO,qBAAqB;AAC5B,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkB,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,OAAO,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMqB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QAClD,IAAIF,KAAK,EAAE;UACT,MAAMG,WAAW,GAAG,MAAMjB,WAAW,CAACkB,UAAU,CAAC,CAAC;UAClDR,OAAO,CAACO,WAAW,CAAC;UACpBX,kBAAkB,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOa,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1CJ,YAAY,CAACM,UAAU,CAAC,cAAc,CAAC;MACzC,CAAC,SAAS;QACRb,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDK,SAAS,CAAC,CAAC;;IAEX;IACA,MAAMS,UAAU,GAAGP,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO;IAC3DJ,QAAQ,CAACU,UAAU,CAAC;IACpBC,QAAQ,CAACC,eAAe,CAACC,YAAY,CAAC,YAAY,EAAEH,UAAU,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,WAAW,GAAGA,CAACC,QAAQ,EAAEb,KAAK,KAAK;IACvCC,YAAY,CAACa,OAAO,CAAC,cAAc,EAAEd,KAAK,CAAC;IAC3CJ,OAAO,CAACiB,QAAQ,CAAC;IACjBrB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMuB,YAAY,GAAGA,CAAA,KAAM;IACzBd,YAAY,CAACM,UAAU,CAAC,cAAc,CAAC;IACvCX,OAAO,CAAC,IAAI,CAAC;IACbJ,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMwB,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,QAAQ,GAAGpB,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IACrDC,QAAQ,CAACmB,QAAQ,CAAC;IAClBhB,YAAY,CAACa,OAAO,CAAC,OAAO,EAAEG,QAAQ,CAAC;IACvCR,QAAQ,CAACC,eAAe,CAACC,YAAY,CAAC,YAAY,EAAEM,QAAQ,CAAC;EAC/D,CAAC;EAED,IAAIxB,SAAS,EAAE;IACb,oBACEL,OAAA;MAAK8B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC/B,OAAA;QAAK8B,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCnC,OAAA;QAAA+B,QAAA,EAAG;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAEV;EAEA,oBACEnC,OAAA,CAACR,MAAM;IAAAuC,QAAA,eACL/B,OAAA;MAAK8B,SAAS,EAAC,KAAK;MAAC,cAAYrB,KAAM;MAAAsB,QAAA,eACrC/B,OAAA,CAACP,MAAM;QAAAsC,QAAA,gBACL/B,OAAA,CAACN,KAAK;UACJ0C,IAAI,EAAC,OAAO;UACZC,OAAO,EACL,CAAClC,eAAe,gBACdH,OAAA,CAACJ,QAAQ;YAAC0C,OAAO,EAAEd;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAElCnC,OAAA,CAACL,QAAQ;YAAC4C,EAAE,EAAC,YAAY;YAACC,OAAO;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAEtC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFnC,OAAA,CAACN,KAAK;UACJ0C,IAAI,EAAC,YAAY;UACjBC,OAAO,EACLlC,eAAe,gBACbH,OAAA,CAACH,aAAa;YACZU,IAAI,EAAEA,IAAK;YACXkC,QAAQ,EAAEd,YAAa;YACvBlB,KAAK,EAAEA,KAAM;YACbiC,aAAa,EAAEd;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,gBAEFnC,OAAA,CAACL,QAAQ;YAAC4C,EAAE,EAAC,OAAO;YAACC,OAAO;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAEjC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFnC,OAAA,CAACN,KAAK;UACJ0C,IAAI,EAAC,GAAG;UACRC,OAAO,eACLrC,OAAA,CAACL,QAAQ;YAAC4C,EAAE,EAAEpC,eAAe,GAAG,YAAY,GAAG,OAAQ;YAACqC,OAAO;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAClE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACjC,EAAA,CAnGQD,GAAG;AAAA0C,EAAA,GAAH1C,GAAG;AAqGZ,eAAeA,GAAG;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}