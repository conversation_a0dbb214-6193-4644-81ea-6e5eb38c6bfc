{"ast": null, "code": "// Authentication service for API interactions\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\nclass AuthService {\n  constructor() {\n    this.baseURL = `${API_BASE_URL}/auth`;\n  }\n\n  // Get authorization headers\n  getAuthHeaders() {\n    const token = localStorage.getItem('access_token');\n    return {\n      'Content-Type': 'application/json',\n      ...(token && {\n        'Authorization': `Bearer ${token}`\n      })\n    };\n  }\n\n  // Handle API responses\n  async handleResponse(response) {\n    const data = await response.json();\n    if (!response.ok) {\n      throw new Error(data.error || `HTTP error! status: ${response.status}`);\n    }\n    return data;\n  }\n\n  // Register a new user\n  async register(userData) {\n    try {\n      const response = await fetch(`${this.baseURL}/register`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(userData)\n      });\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Registration error:', error);\n      throw error;\n    }\n  }\n\n  // Login user\n  async login(username, password) {\n    try {\n      const response = await fetch(`${this.baseURL}/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          username,\n          password\n        })\n      });\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error;\n    }\n  }\n\n  // Get user profile\n  async getProfile() {\n    try {\n      const response = await fetch(`${this.baseURL}/profile`, {\n        method: 'GET',\n        headers: this.getAuthHeaders()\n      });\n      const data = await this.handleResponse(response);\n      return data.user;\n    } catch (error) {\n      console.error('Get profile error:', error);\n      throw error;\n    }\n  }\n\n  // Logout user (client-side only)\n  logout() {\n    localStorage.removeItem('access_token');\n  }\n\n  // Check if user is authenticated\n  isAuthenticated() {\n    const token = localStorage.getItem('access_token');\n    if (!token) return false;\n    try {\n      // Basic token validation (you might want to add JWT decoding here)\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Date.now() / 1000;\n      return payload.exp > currentTime;\n    } catch (error) {\n      console.error('Token validation error:', error);\n      return false;\n    }\n  }\n\n  // Get current user from token\n  getCurrentUser() {\n    const token = localStorage.getItem('access_token');\n    if (!token) return null;\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return payload;\n    } catch (error) {\n      console.error('Token parsing error:', error);\n      return null;\n    }\n  }\n}\n\n// Create and export a singleton instance\nexport const authService = new AuthService();", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "AuthService", "constructor", "baseURL", "getAuthHeaders", "token", "localStorage", "getItem", "handleResponse", "response", "data", "json", "ok", "Error", "error", "status", "register", "userData", "fetch", "method", "headers", "body", "JSON", "stringify", "console", "login", "username", "password", "getProfile", "user", "logout", "removeItem", "isAuthenticated", "payload", "parse", "atob", "split", "currentTime", "Date", "now", "exp", "getCurrentUser", "authService"], "sources": ["A:/EVERYTHING/projects/miniproject/academic_organizer/frontend/src/services/authService.js"], "sourcesContent": ["// Authentication service for API interactions\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\nclass AuthService {\n  constructor() {\n    this.baseURL = `${API_BASE_URL}/auth`;\n  }\n\n  // Get authorization headers\n  getAuthHeaders() {\n    const token = localStorage.getItem('access_token');\n    return {\n      'Content-Type': 'application/json',\n      ...(token && { 'Authorization': `Bearer ${token}` })\n    };\n  }\n\n  // Handle API responses\n  async handleResponse(response) {\n    const data = await response.json();\n    \n    if (!response.ok) {\n      throw new Error(data.error || `HTTP error! status: ${response.status}`);\n    }\n    \n    return data;\n  }\n\n  // Register a new user\n  async register(userData) {\n    try {\n      const response = await fetch(`${this.baseURL}/register`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(userData)\n      });\n\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Registration error:', error);\n      throw error;\n    }\n  }\n\n  // Login user\n  async login(username, password) {\n    try {\n      const response = await fetch(`${this.baseURL}/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ username, password })\n      });\n\n      return await this.handleResponse(response);\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error;\n    }\n  }\n\n  // Get user profile\n  async getProfile() {\n    try {\n      const response = await fetch(`${this.baseURL}/profile`, {\n        method: 'GET',\n        headers: this.getAuthHeaders()\n      });\n\n      const data = await this.handleResponse(response);\n      return data.user;\n    } catch (error) {\n      console.error('Get profile error:', error);\n      throw error;\n    }\n  }\n\n  // Logout user (client-side only)\n  logout() {\n    localStorage.removeItem('access_token');\n  }\n\n  // Check if user is authenticated\n  isAuthenticated() {\n    const token = localStorage.getItem('access_token');\n    if (!token) return false;\n\n    try {\n      // Basic token validation (you might want to add JWT decoding here)\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Date.now() / 1000;\n      \n      return payload.exp > currentTime;\n    } catch (error) {\n      console.error('Token validation error:', error);\n      return false;\n    }\n  }\n\n  // Get current user from token\n  getCurrentUser() {\n    const token = localStorage.getItem('access_token');\n    if (!token) return null;\n\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return payload;\n    } catch (error) {\n      console.error('Token parsing error:', error);\n      return null;\n    }\n  }\n}\n\n// Create and export a singleton instance\nexport const authService = new AuthService();\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,WAAW,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,GAAGN,YAAY,OAAO;EACvC;;EAEA;EACAO,cAAcA,CAAA,EAAG;IACf,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAClD,OAAO;MACL,cAAc,EAAE,kBAAkB;MAClC,IAAIF,KAAK,IAAI;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAG,CAAC;IACrD,CAAC;EACH;;EAEA;EACA,MAAMG,cAAcA,CAACC,QAAQ,EAAE;IAC7B,MAAMC,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;IAElC,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,KAAK,IAAI,uBAAuBL,QAAQ,CAACM,MAAM,EAAE,CAAC;IACzE;IAEA,OAAOL,IAAI;EACb;;EAEA;EACA,MAAMM,QAAQA,CAACC,QAAQ,EAAE;IACvB,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMS,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,WAAW,EAAE;QACvDgB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACN,QAAQ;MAC/B,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAACT,cAAc,CAACC,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMW,KAAKA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC9B,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMS,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,QAAQ,EAAE;QACpDgB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEG,QAAQ;UAAEC;QAAS,CAAC;MAC7C,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAACnB,cAAc,CAACC,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMc,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMS,KAAK,CAAC,GAAG,IAAI,CAACf,OAAO,UAAU,EAAE;QACtDgB,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,IAAI,CAAChB,cAAc,CAAC;MAC/B,CAAC,CAAC;MAEF,MAAMM,IAAI,GAAG,MAAM,IAAI,CAACF,cAAc,CAACC,QAAQ,CAAC;MAChD,OAAOC,IAAI,CAACmB,IAAI;IAClB,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK;IACb;EACF;;EAEA;EACAgB,MAAMA,CAAA,EAAG;IACPxB,YAAY,CAACyB,UAAU,CAAC,cAAc,CAAC;EACzC;;EAEA;EACAC,eAAeA,CAAA,EAAG;IAChB,MAAM3B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAClD,IAAI,CAACF,KAAK,EAAE,OAAO,KAAK;IAExB,IAAI;MACF;MACA,MAAM4B,OAAO,GAAGX,IAAI,CAACY,KAAK,CAACC,IAAI,CAAC9B,KAAK,CAAC+B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;MAErC,OAAON,OAAO,CAACO,GAAG,GAAGH,WAAW;IAClC,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO,KAAK;IACd;EACF;;EAEA;EACA2B,cAAcA,CAAA,EAAG;IACf,MAAMpC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAClD,IAAI,CAACF,KAAK,EAAE,OAAO,IAAI;IAEvB,IAAI;MACF,MAAM4B,OAAO,GAAGX,IAAI,CAACY,KAAK,CAACC,IAAI,CAAC9B,KAAK,CAAC+B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,OAAOH,OAAO;IAChB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,IAAI;IACb;EACF;AACF;;AAEA;AACA,OAAO,MAAM4B,WAAW,GAAG,IAAIzC,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}