/* Dark/Light mode styles */

/* Light theme (default) */
:root,
[data-theme="light"] {
  --bg-color: #ffffff;
  --bg-color-secondary: #f9fafb;
  --bg-color-tertiary: #f3f4f6;
  
  --text-color: #111827;
  --text-color-secondary: #374151;
  --text-color-muted: #6b7280;
  --text-color-inverse: #ffffff;
  
  --border-color: #e5e7eb;
  --border-color-light: #f3f4f6;
  --border-color-dark: #d1d5db;
  
  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-focus-border: #3b82f6;
  
  --card-bg: #ffffff;
  --card-border: #e5e7eb;
  --card-shadow: var(--shadow-base);
  
  --modal-bg: #ffffff;
  --modal-overlay: rgba(0, 0, 0, 0.5);
  
  --button-primary-bg: #3b82f6;
  --button-primary-hover: #2563eb;
  --button-primary-text: #ffffff;
  
  --button-secondary-bg: #f3f4f6;
  --button-secondary-hover: #e5e7eb;
  --button-secondary-text: #374151;
  
  --button-ghost-bg: transparent;
  --button-ghost-hover: #f3f4f6;
  --button-ghost-text: #6b7280;
  
  --header-bg: #ffffff;
  --header-border: #e5e7eb;
  
  --sidebar-bg: #f9fafb;
  --sidebar-border: #e5e7eb;
}

/* Dark theme */
[data-theme="dark"] {
  --bg-color: #111827;
  --bg-color-secondary: #1f2937;
  --bg-color-tertiary: #374151;
  
  --text-color: #f9fafb;
  --text-color-secondary: #d1d5db;
  --text-color-muted: #9ca3af;
  --text-color-inverse: #111827;
  
  --border-color: #374151;
  --border-color-light: #4b5563;
  --border-color-dark: #1f2937;
  
  --input-bg: #1f2937;
  --input-border: #4b5563;
  --input-focus-border: #3b82f6;
  
  --card-bg: #1f2937;
  --card-border: #374151;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  
  --modal-bg: #1f2937;
  --modal-overlay: rgba(0, 0, 0, 0.7);
  
  --button-primary-bg: #3b82f6;
  --button-primary-hover: #2563eb;
  --button-primary-text: #ffffff;
  
  --button-secondary-bg: #374151;
  --button-secondary-hover: #4b5563;
  --button-secondary-text: #d1d5db;
  
  --button-ghost-bg: transparent;
  --button-ghost-hover: #374151;
  --button-ghost-text: #9ca3af;
  
  --header-bg: #1f2937;
  --header-border: #374151;
  
  --sidebar-bg: #111827;
  --sidebar-border: #374151;
  
  /* Override error/success colors for dark theme */
  --color-danger: #f87171;
  --color-success: #34d399;
  --color-warning: #fbbf24;
}

/* Theme-specific component styles */

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid transparent;
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn:disabled,
.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border-color: var(--button-primary-bg);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--button-primary-hover);
  border-color: var(--button-primary-hover);
}

.btn-secondary {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--button-secondary-hover);
}

.btn-ghost {
  background-color: var(--button-ghost-bg);
  color: var(--button-ghost-text);
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--button-ghost-hover);
}

.btn-danger {
  background-color: var(--color-danger);
  color: var(--color-white);
  border-color: var(--color-danger);
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
  border-color: #dc2626;
}

/* Button sizes */
.btn-small {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
}

.btn-medium {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
}

.btn-large {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-lg);
}

/* Button loading state */
.btn-loading .btn-text-loading {
  opacity: 0;
}

.btn-spinner {
  position: absolute;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

/* Cards */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-6);
  transition: box-shadow var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal-backdrop);
  padding: var(--spacing-4);
}

.modal {
  background-color: var(--modal-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-height: 90vh;
  overflow-y: auto;
  z-index: var(--z-modal);
  animation: modalSlideIn 0.2s ease-out;
}

.modal-small { max-width: 400px; width: 100%; }
.modal-medium { max-width: 600px; width: 100%; }
.modal-large { max-width: 800px; width: 100%; }
.modal-fullscreen { 
  max-width: 95vw; 
  max-height: 95vh; 
  width: 100%; 
  height: 100%; 
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6) var(--spacing-6) var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin: 0;
}

.modal-close-btn {
  font-size: var(--font-size-2xl);
  line-height: 1;
  padding: var(--spacing-1);
}

.modal-content {
  padding: var(--spacing-6);
}

/* Theme toggle button */
.theme-toggle {
  font-size: var(--font-size-lg);
  padding: var(--spacing-2);
  border-radius: var(--radius-full);
}

/* Responsive design for themes */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    --bg-color: #111827;
    --bg-color-secondary: #1f2937;
    --bg-color-tertiary: #374151;
    
    --text-color: #f9fafb;
    --text-color-secondary: #d1d5db;
    --text-color-muted: #9ca3af;
    
    --border-color: #374151;
    --input-bg: #1f2937;
    --card-bg: #1f2937;
    --modal-bg: #1f2937;
  }
}
