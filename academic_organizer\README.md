# Academic Organizer

A comprehensive document management system designed specifically for academic resources. Organize your research papers, assignments, notes, and other academic documents with ease.

## Features

- **User Authentication**: Secure registration and login system
- **Document Management**: Upload, organize, and manage academic documents
- **Categorization**: Create custom categories to organize your documents
- **Tagging System**: Add tags to documents for better searchability
- **Search & Filter**: Powerful search and filtering capabilities
- **Dark/Light Theme**: Toggle between dark and light modes
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **File Type Support**: Supports PDF, DOC, DOCX, TXT, images, presentations, and spreadsheets

## Technology Stack

### Backend
- **Flask**: Python web framework
- **SQLAlchemy**: ORM for database operations
- **Flask-JWT-Extended**: JWT authentication
- **Flask-CORS**: Cross-origin resource sharing
- **SQLite**: Database (easily configurable to other databases)

### Frontend
- **React**: JavaScript library for building user interfaces
- **React Router**: Client-side routing
- **CSS3**: Modern styling with CSS custom properties
- **Responsive Design**: Mobile-first approach

## Project Structure

```
academic_organizer/
├── backend/
│   ├── app.py                     # Flask main application
│   ├── config.py                  # Database and app configurations
│   ├── models.py                  # Database models (SQLAlchemy ORM)
│   └── routes/                    # API endpoints
│       ├── auth_routes.py         # Authentication routes
│       ├── document_routes.py     # Document management routes
│       └── ...
├── frontend/
│   ├── public/
│   │   ├── index.html             # Main HTML file
│   │   └── assets/
│   │       ├── images/
│   │       └── fonts/
│   ├── src/
│   │   ├── App.js                 # Main React component
│   │   ├── components/            # Reusable UI components
│   │   │   ├── Button.js
│   │   │   ├── Modal.js
│   │   │   └── ...
│   │   ├── pages/                 # Page-level components
│   │   │   ├── AuthPage.js
│   │   │   ├── DashboardPage.js
│   │   │   └── ...
│   │   ├── styles/                # Global and component-specific styles
│   │   │   ├── base.css           # Resets, global variables, typography
│   │   │   ├── themes.css         # Dark/Light mode styles
│   │   │   └── animations.css     # Keyframe animations
│   │   └── services/              # API interaction logic
│   │       ├── authService.js
│   │       └── documentService.js
│   └── package.json               # Frontend dependencies
├── database/
│   └── schema.sql                 # SQL script to create tables
└── README.md
```

## Installation & Setup

### Prerequisites
- Python 3.8+
- Node.js 16+
- npm or yarn

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd academic_organizer/backend
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install Python dependencies:
   ```bash
   pip install flask flask-sqlalchemy flask-jwt-extended flask-cors flask-user werkzeug
   ```

4. Set up environment variables (create a `.env` file):
   ```env
   SECRET_KEY=your-secret-key-here
   JWT_SECRET_KEY=your-jwt-secret-key-here
   DATABASE_URL=sqlite:///academic_organizer.db
   UPLOAD_FOLDER=uploads
   ```

5. Initialize the database:
   ```bash
   python -c "from app import create_app; from models import db; app = create_app(); app.app_context().push(); db.create_all()"
   ```

6. Run the Flask application:
   ```bash
   python app.py
   ```

The backend will be available at `http://localhost:5000`

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd academic_organizer/frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create environment variables (create a `.env` file):
   ```env
   REACT_APP_API_URL=http://localhost:5000/api
   ```

4. Start the development server:
   ```bash
   npm start
   ```

The frontend will be available at `http://localhost:3000`

## Usage

1. **Registration**: Create a new account with your email and personal information
2. **Login**: Sign in with your username and password
3. **Upload Documents**: Use the upload button to add your academic documents
4. **Organize**: Create categories and add tags to organize your documents
5. **Search**: Use the search bar to find specific documents
6. **Filter**: Filter documents by category or other criteria
7. **Theme**: Toggle between light and dark themes using the theme button

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/profile` - Get user profile (requires authentication)

### Documents
- `GET /api/documents/` - Get all documents for the current user
- `POST /api/documents/upload` - Upload a new document
- `GET /api/documents/{id}` - Get a specific document
- `DELETE /api/documents/{id}` - Delete a document

## Database Schema

The application uses the following main tables:
- **users**: User account information
- **documents**: Document metadata and file information
- **categories**: User-defined categories
- **document_tags**: Tags associated with documents

See `database/schema.sql` for the complete database schema.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

If you encounter any issues or have questions, please open an issue on the GitHub repository.

## Future Enhancements

- Document sharing between users
- Version control for documents
- Advanced search with full-text indexing
- Document preview functionality
- Bulk operations (upload, delete, categorize)
- Export/import functionality
- Mobile app
- Integration with cloud storage services
- Collaborative features
- Document templates
