{"ast": null, "code": "var _jsxFileName = \"A:\\\\EVERYTHING\\\\projects\\\\miniproject\\\\academic_organizer\\\\frontend\\\\src\\\\components\\\\Modal.js\",\n  _s = $RefreshSig$();\n// Reusable Modal component\nimport React, { useEffect } from 'react';\nimport Button from './Button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Modal = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'medium',\n  showCloseButton = true,\n  closeOnOverlayClick = true,\n  className = ''\n}) => {\n  _s();\n  useEffect(() => {\n    const handleEscape = event => {\n      if (event.key === 'Escape' && isOpen) {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  const sizeClasses = {\n    small: 'modal-small',\n    medium: 'modal-medium',\n    large: 'modal-large',\n    fullscreen: 'modal-fullscreen'\n  };\n  const handleOverlayClick = event => {\n    if (event.target === event.currentTarget && closeOnOverlayClick) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    onClick: handleOverlayClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `modal ${sizeClasses[size]} ${className}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"modal-title\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), showCloseButton && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"ghost\",\n          size: \"small\",\n          onClick: onClose,\n          className: \"modal-close-btn\",\n          \"aria-label\": \"Close modal\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(Modal, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Modal;\nexport default Modal;\nvar _c;\n$RefreshReg$(_c, \"Modal\");", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Modal", "isOpen", "onClose", "title", "children", "size", "showCloseButton", "closeOnOverlayClick", "className", "_s", "handleEscape", "event", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "sizeClasses", "small", "medium", "large", "fullscreen", "handleOverlayClick", "target", "currentTarget", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "_c", "$RefreshReg$"], "sources": ["A:/EVERYTHING/projects/miniproject/academic_organizer/frontend/src/components/Modal.js"], "sourcesContent": ["// Reusable Modal component\nimport React, { useEffect } from 'react';\nimport Button from './Button';\n\nconst Modal = ({ \n  isOpen, \n  onClose, \n  title, \n  children, \n  size = 'medium',\n  showCloseButton = true,\n  closeOnOverlayClick = true,\n  className = ''\n}) => {\n  useEffect(() => {\n    const handleEscape = (event) => {\n      if (event.key === 'Escape' && isOpen) {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const sizeClasses = {\n    small: 'modal-small',\n    medium: 'modal-medium',\n    large: 'modal-large',\n    fullscreen: 'modal-fullscreen'\n  };\n\n  const handleOverlayClick = (event) => {\n    if (event.target === event.currentTarget && closeOnOverlayClick) {\n      onClose();\n    }\n  };\n\n  return (\n    <div className=\"modal-overlay\" onClick={handleOverlayClick}>\n      <div className={`modal ${sizeClasses[size]} ${className}`}>\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">{title}</h2>\n          {showCloseButton && (\n            <Button\n              variant=\"ghost\"\n              size=\"small\"\n              onClick={onClose}\n              className=\"modal-close-btn\"\n              aria-label=\"Close modal\"\n            >\n              ×\n            </Button>\n          )}\n        </div>\n        <div className=\"modal-content\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,KAAK,GAAGA,CAAC;EACbC,MAAM;EACNC,OAAO;EACPC,KAAK;EACLC,QAAQ;EACRC,IAAI,GAAG,QAAQ;EACfC,eAAe,GAAG,IAAI;EACtBC,mBAAmB,GAAG,IAAI;EAC1BC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJb,SAAS,CAAC,MAAM;IACd,MAAMc,YAAY,GAAIC,KAAK,IAAK;MAC9B,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,IAAIX,MAAM,EAAE;QACpCC,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVY,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,YAAY,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAAChB,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErB,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMkB,WAAW,GAAG;IAClBC,KAAK,EAAE,aAAa;IACpBC,MAAM,EAAE,cAAc;IACtBC,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAE;EACd,CAAC;EAED,MAAMC,kBAAkB,GAAIb,KAAK,IAAK;IACpC,IAAIA,KAAK,CAACc,MAAM,KAAKd,KAAK,CAACe,aAAa,IAAInB,mBAAmB,EAAE;MAC/DL,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEH,OAAA;IAAKS,SAAS,EAAC,eAAe;IAACmB,OAAO,EAAEH,kBAAmB;IAAApB,QAAA,eACzDL,OAAA;MAAKS,SAAS,EAAE,SAASW,WAAW,CAACd,IAAI,CAAC,IAAIG,SAAS,EAAG;MAAAJ,QAAA,gBACxDL,OAAA;QAAKS,SAAS,EAAC,cAAc;QAAAJ,QAAA,gBAC3BL,OAAA;UAAIS,SAAS,EAAC,aAAa;UAAAJ,QAAA,EAAED;QAAK;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACvCzB,eAAe,iBACdP,OAAA,CAACF,MAAM;UACLmC,OAAO,EAAC,OAAO;UACf3B,IAAI,EAAC,OAAO;UACZsB,OAAO,EAAEzB,OAAQ;UACjBM,SAAS,EAAC,iBAAiB;UAC3B,cAAW,aAAa;UAAAJ,QAAA,EACzB;QAED;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNhC,OAAA;QAAKS,SAAS,EAAC,eAAe;QAAAJ,QAAA,EAC3BA;MAAQ;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CAlEIT,KAAK;AAAAiC,EAAA,GAALjC,KAAK;AAoEX,eAAeA,KAAK;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}