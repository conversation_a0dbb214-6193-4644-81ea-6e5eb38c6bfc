#!/usr/bin/env python3
"""
Debug server startup script
"""

import sys
import traceback
import os

def main():
    print("🔍 Debug: Starting server with detailed error reporting...")
    print(f"🔍 Debug: Python version: {sys.version}")
    print(f"🔍 Debug: Current working directory: {os.getcwd()}")
    print(f"🔍 Debug: Python path: {sys.path}")
    
    try:
        print("🔍 Debug: Importing Flask...")
        import flask
        print(f"✅ Flask version: {flask.__version__}")
        
        print("🔍 Debug: Importing app...")
        from app import create_app
        print("✅ App module imported successfully")
        
        print("🔍 Debug: Creating app instance...")
        app = create_app()
        print("✅ App instance created successfully")
        
        print("🔍 Debug: Checking routes...")
        for rule in app.url_map.iter_rules():
            print(f"  📍 {rule.rule} -> {rule.endpoint}")
        
        print("🔍 Debug: Starting Flask server...")
        print("🌐 Server will be available at: http://localhost:5000")
        print("📡 API endpoints at: http://localhost:5000/api")
        print("⏹️  Press Ctrl+C to stop")
        print("-" * 60)
        
        # Start the server
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False,
            threaded=True
        )
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        print("📋 Full traceback:")
        traceback.print_exc()
        
    finally:
        print("🔍 Debug: Server startup process completed")

if __name__ == '__main__':
    main()
