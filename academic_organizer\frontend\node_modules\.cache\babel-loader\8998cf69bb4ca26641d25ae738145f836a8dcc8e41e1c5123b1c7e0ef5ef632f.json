{"ast": null, "code": "var _jsxFileName = \"A:\\\\EVERYTHING\\\\projects\\\\miniproject\\\\academic_organizer\\\\frontend\\\\src\\\\components\\\\Button.js\";\n// Reusable Button component\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Button = ({\n  children,\n  variant = 'primary',\n  size = 'medium',\n  disabled = false,\n  loading = false,\n  onClick,\n  type = 'button',\n  className = '',\n  ...props\n}) => {\n  const baseClasses = 'btn';\n  const variantClasses = {\n    primary: 'btn-primary',\n    secondary: 'btn-secondary',\n    danger: 'btn-danger',\n    ghost: 'btn-ghost'\n  };\n  const sizeClasses = {\n    small: 'btn-small',\n    medium: 'btn-medium',\n    large: 'btn-large'\n  };\n  const classes = [baseClasses, variantClasses[variant], sizeClasses[size], disabled && 'btn-disabled', loading && 'btn-loading', className].filter(Boolean).join(' ');\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    type: type,\n    className: classes,\n    disabled: disabled || loading,\n    onClick: onClick,\n    ...props,\n    children: [loading && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"btn-spinner\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: loading ? 'btn-text-loading' : '',\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "children", "variant", "size", "disabled", "loading", "onClick", "type", "className", "props", "baseClasses", "variantClasses", "primary", "secondary", "danger", "ghost", "sizeClasses", "small", "medium", "large", "classes", "filter", "Boolean", "join", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["A:/EVERYTHING/projects/miniproject/academic_organizer/frontend/src/components/Button.js"], "sourcesContent": ["// Reusable Button component\nimport React from 'react';\n\nconst Button = ({ \n  children, \n  variant = 'primary', \n  size = 'medium', \n  disabled = false, \n  loading = false,\n  onClick,\n  type = 'button',\n  className = '',\n  ...props \n}) => {\n  const baseClasses = 'btn';\n  const variantClasses = {\n    primary: 'btn-primary',\n    secondary: 'btn-secondary',\n    danger: 'btn-danger',\n    ghost: 'btn-ghost'\n  };\n  const sizeClasses = {\n    small: 'btn-small',\n    medium: 'btn-medium',\n    large: 'btn-large'\n  };\n\n  const classes = [\n    baseClasses,\n    variantClasses[variant],\n    sizeClasses[size],\n    disabled && 'btn-disabled',\n    loading && 'btn-loading',\n    className\n  ].filter(Boolean).join(' ');\n\n  return (\n    <button\n      type={type}\n      className={classes}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...props}\n    >\n      {loading && <span className=\"btn-spinner\"></span>}\n      <span className={loading ? 'btn-text-loading' : ''}>{children}</span>\n    </button>\n  );\n};\n\nexport default Button;\n"], "mappings": ";AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAC;EACdC,QAAQ;EACRC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,QAAQ;EACfC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,OAAO;EACPC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG,EAAE;EACd,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG,KAAK;EACzB,MAAMC,cAAc,GAAG;IACrBC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE,YAAY;IACpBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMC,WAAW,GAAG;IAClBC,KAAK,EAAE,WAAW;IAClBC,MAAM,EAAE,YAAY;IACpBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,OAAO,GAAG,CACdV,WAAW,EACXC,cAAc,CAACT,OAAO,CAAC,EACvBc,WAAW,CAACb,IAAI,CAAC,EACjBC,QAAQ,IAAI,cAAc,EAC1BC,OAAO,IAAI,aAAa,EACxBG,SAAS,CACV,CAACa,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAE3B,oBACExB,OAAA;IACEQ,IAAI,EAAEA,IAAK;IACXC,SAAS,EAAEY,OAAQ;IACnBhB,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;IAC9BC,OAAO,EAAEA,OAAQ;IAAA,GACbG,KAAK;IAAAR,QAAA,GAERI,OAAO,iBAAIN,OAAA;MAAMS,SAAS,EAAC;IAAa;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACjD5B,OAAA;MAAMS,SAAS,EAAEH,OAAO,GAAG,kBAAkB,GAAG,EAAG;MAAAJ,QAAA,EAAEA;IAAQ;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/D,CAAC;AAEb,CAAC;AAACC,EAAA,GA7CI5B,MAAM;AA+CZ,eAAeA,MAAM;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}